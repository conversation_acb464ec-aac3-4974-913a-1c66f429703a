#!/usr/bin/env node

/**
 * Firebase Connection Test
 * 
 * Quick test to verify Firebase configuration and connection
 */

require('dotenv').config({ path: '.env.local' });

// Test environment variables
console.log('🔥 Testing Firebase Configuration...\n');

const requiredVars = [
  'NEXT_PUBLIC_FIREBASE_API_KEY',
  'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
  'NEXT_PUBLIC_FIREBASE_DATABASE_URL'
];

console.log('📋 Environment Variables:');
requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${varName}: Not set`);
  }
});

console.log('\n🔗 Firebase Configuration:');
console.log(`Project ID: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}`);
console.log(`Database URL: ${process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL}`);
console.log(`Auth Domain: ${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}`);

console.log('\n📊 Usage Limits:');
console.log(`Daily Read Limit: ${process.env.FIREBASE_DAILY_READ_LIMIT || '10000'}`);
console.log(`Daily Write Limit: ${process.env.FIREBASE_DAILY_WRITE_LIMIT || '5000'}`);
console.log(`Monthly Cost Limit: $${process.env.FIREBASE_MONTHLY_COST_LIMIT || '25'}`);

console.log('\n🎯 Next Steps:');
console.log('1. Go to Firebase Console: https://console.firebase.google.com/');
console.log('2. Select project: hifnf-70751');
console.log('3. Set up Realtime Database');
console.log('4. Apply security rules from firebase-database-rules.json');
console.log('5. Test with: npm run dev');

console.log('\n✅ Configuration test completed!');
console.log('Your Firebase setup is ready for manual configuration in Firebase Console.');
