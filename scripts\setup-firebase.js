#!/usr/bin/env node

/**
 * Firebase Setup Script
 * 
 * This script helps set up Firebase Realtime Database with proper security rules
 * and initial configuration for the HIFNF project.
 */

const fs = require('fs');
const path = require('path');

console.log('🔥 Firebase Setup Script for HIFNF');
console.log('=====================================\n');

// Check if environment variables are set
function checkEnvironmentVariables() {
  console.log('1. Checking environment variables...');
  
  const requiredVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_DATABASE_URL',
    'FIREBASE_PRIVATE_KEY',
    'FIREBASE_CLIENT_EMAIL'
  ];

  const missing = [];
  
  // Load .env.local
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    requiredVars.forEach(varName => {
      if (!envContent.includes(varName)) {
        missing.push(varName);
      }
    });
  } else {
    console.log('❌ .env.local file not found!');
    return false;
  }

  if (missing.length > 0) {
    console.log('❌ Missing environment variables:');
    missing.forEach(varName => console.log(`   - ${varName}`));
    return false;
  }

  console.log('✅ All environment variables are set\n');
  return true;
}

// Check Firebase configuration
function checkFirebaseConfig() {
  console.log('2. Checking Firebase configuration...');
  
  const configPath = path.join(process.cwd(), 'src/lib/firebase/config.ts');
  if (!fs.existsSync(configPath)) {
    console.log('❌ Firebase config file not found!');
    return false;
  }

  console.log('✅ Firebase configuration file exists\n');
  return true;
}

// Check security rules
function checkSecurityRules() {
  console.log('3. Checking security rules...');
  
  const rulesPath = path.join(process.cwd(), 'firebase-database-rules.json');
  if (!fs.existsSync(rulesPath)) {
    console.log('❌ Security rules file not found!');
    return false;
  }

  try {
    const rules = JSON.parse(fs.readFileSync(rulesPath, 'utf8'));
    if (!rules.rules) {
      console.log('❌ Invalid security rules format!');
      return false;
    }
    
    console.log('✅ Security rules file is valid\n');
    return true;
  } catch (error) {
    console.log('❌ Error parsing security rules:', error.message);
    return false;
  }
}

// Display setup instructions
function displaySetupInstructions() {
  console.log('4. Manual Setup Instructions:');
  console.log('=============================\n');
  
  console.log('📋 Firebase Console Setup:');
  console.log('1. Go to https://console.firebase.google.com/');
  console.log('2. Select your project: hifnf-70751');
  console.log('3. Go to "Realtime Database" section');
  console.log('4. If not created, click "Create Database"');
  console.log('5. Choose location closest to your users');
  console.log('6. Start in "Test mode" (we\'ll add rules next)\n');
  
  console.log('🔒 Security Rules Setup:');
  console.log('1. In Realtime Database, go to "Rules" tab');
  console.log('2. Copy content from "firebase-database-rules.json"');
  console.log('3. Paste it in the rules editor');
  console.log('4. Click "Publish"\n');
  
  console.log('🧪 Testing Setup:');
  console.log('1. Start your development server: npm run dev');
  console.log('2. Open browser console');
  console.log('3. Look for "🔥 Firebase initialized successfully"');
  console.log('4. Test messaging between two browser windows\n');
  
  console.log('📊 Usage Monitoring:');
  console.log('1. Monitor usage in Firebase Console');
  console.log('2. Check browser console for usage stats');
  console.log('3. Set up alerts for cost thresholds\n');
}

// Create test data structure
function createTestDataStructure() {
  console.log('5. Creating test data structure...');
  
  const testData = {
    "conversations": {
      "user1_user2": {
        "participants": ["user1", "user2"],
        "lastMessage": {
          "content": "Hello! This is a test message.",
          "timestamp": Date.now(),
          "senderId": "user1"
        },
        "unreadCount": {
          "user1": 0,
          "user2": 1
        },
        "updatedAt": Date.now()
      }
    },
    "messages": {
      "user1_user2": {
        "test_message_1": {
          "senderId": "user1",
          "receiverId": "user2",
          "content": "Hello! This is a test message.",
          "timestamp": Date.now(),
          "status": "sent",
          "type": "text"
        }
      }
    },
    "presence": {
      "user1": {
        "online": true,
        "lastSeen": Date.now(),
        "connectedAt": Date.now()
      }
    }
  };

  const testDataPath = path.join(process.cwd(), 'firebase-test-data.json');
  fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
  
  console.log('✅ Test data structure created: firebase-test-data.json');
  console.log('   You can import this in Firebase Console for testing\n');
}

// Display cost optimization tips
function displayCostOptimizationTips() {
  console.log('💰 Cost Optimization Tips:');
  console.log('==========================\n');
  
  console.log('📈 Free Tier Limits:');
  console.log('- 1GB stored data');
  console.log('- 10GB/month bandwidth');
  console.log('- 100 concurrent connections\n');
  
  console.log('🎯 Optimization Strategies:');
  console.log('- Use flat data structure (already implemented)');
  console.log('- Limit query results with limitToLast()');
  console.log('- Implement automatic data cleanup');
  console.log('- Monitor usage with built-in monitoring');
  console.log('- Use offline capabilities to reduce bandwidth\n');
  
  console.log('⚠️  Cost Alerts:');
  console.log('- Set up usage monitoring (already implemented)');
  console.log('- Configure cost thresholds in .env.local');
  console.log('- Monitor Firebase Console usage section\n');
}

// Main setup function
function runSetup() {
  console.log('Starting Firebase setup validation...\n');
  
  let allChecksPass = true;
  
  // Run all checks
  allChecksPass &= checkEnvironmentVariables();
  allChecksPass &= checkFirebaseConfig();
  allChecksPass &= checkSecurityRules();
  
  if (allChecksPass) {
    console.log('🎉 All checks passed! Your Firebase setup is ready.\n');
    
    // Create test data
    createTestDataStructure();
    
    // Display instructions
    displaySetupInstructions();
    displayCostOptimizationTips();
    
    console.log('🚀 Next Steps:');
    console.log('1. Follow the manual setup instructions above');
    console.log('2. Test the real-time features');
    console.log('3. Monitor usage and costs');
    console.log('4. Optimize based on your usage patterns\n');
    
    console.log('📚 Documentation:');
    console.log('- Setup Guide: FIREBASE_SETUP_GUIDE.md');
    console.log('- Cost Analysis: FIREBASE_COST_ANALYSIS.md\n');
    
  } else {
    console.log('❌ Some checks failed. Please fix the issues above and run again.\n');
    process.exit(1);
  }
}

// Run the setup
runSetup();
